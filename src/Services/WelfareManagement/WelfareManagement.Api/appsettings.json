{"Kestrel": {"Endpoints": {"Http": {"Url": "http://0.0.0.0:5005"}}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"], "MinimumLevel": "Information", "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "Elasticsearch", "Args": {"nodeUris": "http://**********:9200/", "indexFormat": "s3-mol.ils-{0:yyyy.MM}", "autoRegisterTemplate": true, "autoRegisterTemplateVersion": "ESv6"}}, {"Name": "File", "Args": {"path": "Logs\\.log", "rollingInterval": "Day"}}]}, "WelfareManagementConfiguration": {"RunAsWindowsService": false, "ConnectionString": "Server=localhost,1433;Database=Mol;User Id=sa;Password=StrongP@ssw0rd!;TrustServerCertificate=True;", "CreateCareRequestSheetAtDay": 15, "Messaging": {"Host": {"HostUrl": "rabbitmq://**********:5672/", "VirtualHost": "/", "Username": "admin", "Password": "admin", "HeartbeatsInSeconds": 5}, "Consumers": []}}, "Origins": "*"}