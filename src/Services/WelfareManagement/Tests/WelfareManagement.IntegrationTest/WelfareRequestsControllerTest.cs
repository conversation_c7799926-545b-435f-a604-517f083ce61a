using System.Net.Http.Headers;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.DependencyInjection;
using S3.Core.Application.Enums;
using S3.Core.Application.Models;
using S3.Core.Application.Models.CQRS;
using S3.Core.Security.Models;
using S3.Core.Test.Models;
using S3.MoL.WelfareManagement.Api.WelfareRequests.Dtos;
using S3.MoL.WelfareManagement.Domain.Entities;
using S3.MoL.WelfareManagement.Domain.Enums;
using S3.MoL.WelfareManagement.Infrastructure.Services.Persistence;

namespace S3.MoL.WelfareManagement.IntegrationTest;

[TestFixture]
internal sealed class WelfareRequestsControllerTest : VirtualWebServer<Program>
{
    private WelfareManagementDbContext _welfareManagementDbContext = null!;

    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.UseEnvironment("Testing"); // Ensure test configuration loads
    }

    public WelfareRequestsControllerTest()
    {
        RestClient.DefaultRequestHeaders.Authorization = AppUtility.CreateAuthenticationHeader(
            role: Roles.SystemAdmin,
            customClaims:
            [
                new KeyValuePair<string, string>(AppConstants.Claim_UserId, "abc@123"),
                new KeyValuePair<string, string>(AppConstants.Claim_PreferredUserName, "userName"),
                new KeyValuePair<string, string>(AppConstants.Claim_OrganizationUnitId, "1"),
                new KeyValuePair<string, string>(AppConstants.Claim_Role, Roles.Researcher),
                new KeyValuePair<string, string>(AppConstants.Claim_Role, Roles.Reviewer),
                new KeyValuePair<string, string>(AppConstants.Claim_Role, Roles.DirectorateManager),
                new KeyValuePair<string, string>(AppConstants.Claim_Permissions, Permissions.CreateWelfareRequest),
                new KeyValuePair<string, string>(AppConstants.Claim_Permissions, Permissions.SearchWelfareRequest),
                new KeyValuePair<string, string>(AppConstants.Claim_Permissions, Permissions.EditWelfareRequest)
            ]);
        RestClient.DefaultRequestHeaders.AcceptLanguage.Add(
            new StringWithQualityHeaderValue(AppConstants.EnglishLanguage));
    }

    protected override void OverrideServices(IServiceCollection services)
    {
        _welfareManagementDbContext = MockDataContext<WelfareManagementDbContext>(services);
        base.OverrideServices(services);
    }

    protected override async void SeedTestData()
    {
        try
        {
            // Seed Occupations
            var occupations = new List<Occupation>
            {
                new()
                {
                    OccupationId = 1,
                    Code = "OCC001",
                    Text = "Engineer",
                    IsActive = true,
                    IsDeleted = false
                }
            };
            _welfareManagementDbContext.Occupations.AddRange(occupations);

            // Seed WelfareCategories
            var welfareCategories = new List<WelfareCategory>
            {
                new()
                {
                    WelfareCategoryId = 1,
                    Code = "WC001",
                    Text = "Social Welfare"
                }
            };
            _welfareManagementDbContext.WelfareCategories.AddRange(welfareCategories);

            // Seed WelfareTypes
            var welfareTypes = new List<WelfareType>
            {
                new()
                {
                    WelfareTypeId = (int)WelfareTypes.Marriage,
                    Code = "WT-M_01",
                    Text = "Marriage",
                    WelfareCategoryId = 1,
                    MaximumLimit = 1
                },
                new()
                {
                    WelfareTypeId = (int)WelfareTypes.NewBorn,
                    Code = "WT-M_02",
                    Text = "Newborn",
                    WelfareCategoryId = 1,
                    MaximumLimit = 2
                }
            };
            _welfareManagementDbContext.WelfareTypes.AddRange(welfareTypes);

            // Seed Labors
            var labors = new List<Labor>
            {
                new()
                {
                    LaborId = 1L,
                    NationalId = "12345678901234",
                    FullName = "John Doe",
                    MobileNo = "1234567890123",
                    OccupationId = 1,
                    HasFullDisability = false, // IsBeneficiary will be true (computed)
                    DeathDate = null // Ensure not dead
                },
                new()
                {
                    LaborId = 2L,
                    NationalId = "12345678901235",
                    FullName = "Jane Smith",
                    MobileNo = "1234567890124",
                    OccupationId = 1,
                    HasFullDisability = true, // IsBeneficiary will be false (computed)
                    DeathDate = null // Ensure not dead
                },
                new()
                {
                    LaborId = 3L,
                    NationalId = "12345678901236",
                    FullName = "Bob Johnson",
                    MobileNo = "1234567890125",
                    OccupationId = 1,
                    HasFullDisability = false, // IsBeneficiary will be true (computed)
                    DeathDate = null // Ensure not dead
                }
            };
            _welfareManagementDbContext.Labors.AddRange(labors);

            // Seed RequestStatuses
            var requestStatuses = new List<RequestStatus>
            {
                new()
                {
                    RequestStatusId = (int)RequestStatuses.InProgress,
                    Code = "InProgress",
                    Text = "InProgress"
                },
                new()
                {
                    RequestStatusId = (int)RequestStatuses.Completed,
                    Code = "Completed",
                    Text = "Completed"
                }
            };
            _welfareManagementDbContext.RequestStatuses.AddRange(requestStatuses);

            // Seed WelfareRequestStatuses (required for WelfareRequest)
            var welfareRequestStatuses = new List<WelfareRequestStatus>
            {
                new()
                {
                    WelfareRequestStatusId = WelfareRequestStatuses.Submitted,
                    Code = "Submitted",
                    Text = "Submitted"
                },
                new()
                {
                    WelfareRequestStatusId = WelfareRequestStatuses.Approved,
                    Code = "Approved",
                    Text = "Approved"
                }
            };
            _welfareManagementDbContext.WelfareRequestStatuses.AddRange(welfareRequestStatuses);

            // Seed Directorates (required for WelfareRequest)
            // Note: GovernorateId 398 corresponds to Cairo from the database scripts
            var directorates = new List<Directorate>
            {
                new()
                {
                    DirectorateId = 1,
                    Code = "DIR001",
                    Text = "Test Directorate",
                    IsActive = true,
                    IsDeleted = false,
                    GovernorateId = 398 // Cairo
                }
            };
            _welfareManagementDbContext.Directorates.AddRange(directorates);

            // Seed a completed welfare request for Labor 3 to test limit exceeded scenario
            var completedRequest = new SocialWelfareRequest
            {
                LaborId = 3L,
                RequestStatusId = (int)RequestStatuses.Completed,
                WelfareRequestStatusId = WelfareRequestStatuses.Approved,
                RequestNo = "REQ001",
                DirectorateId = 1,
                EventDate = DateOnly.FromDateTime(DateTime.Now),
                NationalId = "12345678901236",
                CreatedDate = DateTime.UtcNow,
                CreatedByUserId = "test",
                CreatedByUserName = "test",
                Version = new byte[8],
                DueAmount = 1000
            };

            // Use reflection to set the protected WelfareTypeId property
            var welfareTypeIdProperty = typeof(WelfareRequest).GetProperty(nameof(WelfareRequest.WelfareTypeId));
            welfareTypeIdProperty?.SetValue(completedRequest, (int)WelfareTypes.Marriage);

            _welfareManagementDbContext.SocialWelfareRequests.Add(completedRequest);

            // Seed an in-progress welfare request for Labor 1 to test conflict scenario
            var inProgressRequest = new SocialWelfareRequest
            {
                LaborId = 1L,
                RequestStatusId = (int)RequestStatuses.InProgress,
                WelfareRequestStatusId = WelfareRequestStatuses.Submitted,
                RequestNo = "REQ002",
                DirectorateId = 1,
                EventDate = DateOnly.FromDateTime(DateTime.Now),
                NationalId = "12345678901234",
                CreatedDate = DateTime.UtcNow,
                CreatedByUserId = "test",
                CreatedByUserName = "test",
                Version = new byte[8],
                DueAmount = 500
            };

            welfareTypeIdProperty?.SetValue(inProgressRequest, (int)WelfareTypes.NewBorn);
            _welfareManagementDbContext.SocialWelfareRequests.Add(inProgressRequest);

            var x = await _welfareManagementDbContext.SaveChangesAsync();
            Console.WriteLine($"bedoo ${x}");
            Console.WriteLine($"bedooooo ${_welfareManagementDbContext.Labors.Count()}");
        }
        catch (Exception e)
        {
            throw e;
        }
    }


    [Test]
    public async Task CheckWelfareType_ShouldReturnOk_WithEligibleTrue_WhenAllConditionsAreMet()
    {
        // Arrange
        var laborId = 1L;
        var welfareTypeId = (int)WelfareTypes.Marriage;

        // Act
        var result = await GetAsync<Result<CheckWelfareTypeResponseDto>>(
            $"WelfareRequests/{laborId}/{welfareTypeId}",
            AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Result should not be null.");
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.Ok), "API response status should be OK.");
            Assert.That(result.Value, Is.Not.Null, "Returned data should not be null.");
            Assert.That(result.Value!.IsBeneficairy, Is.True, "Should be eligible for welfare.");
        });
    }

    [Test]
    public async Task CheckWelfareType_ShouldReturnNotFound_WhenLaborDoesNotExist()
    {
        // Arrange
        long laborId = 999; // Non-existent labor
        var welfareTypeId = (int)WelfareTypes.Marriage;

        // Act
        var result = await GetAsync<Result<CheckWelfareTypeResponseDto>>(
            $"WelfareRequests/{laborId}/{welfareTypeId}",
            AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Result should not be null.");
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.NotFound), "API response status should be NotFound.");
            Assert.That(result.Value, Is.Null, "Returned data should be null.");
        });
    }

    [Test]
    public async Task CheckWelfareType_ShouldReturnNotFound_WhenWelfareTypeDoesNotExist()
    {
        // Arrange
        var laborId = 1L;
        var welfareTypeId = 999; // Non-existent welfare type

        // Act
        var result = await GetAsync<Result<CheckWelfareTypeResponseDto>>(
            $"WelfareRequests/{laborId}/{welfareTypeId}",
            AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Result should not be null.");
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.NotFound), "API response status should be NotFound.");
            Assert.That(result.Value, Is.Null, "Returned data should be null.");
        });
    }


    [Test]
    public async Task CheckWelfareType_ShouldReturnForbidden_WhenLaborIsNotBeneficiary()
    {
        // Arrange
        long laborId = 2; // Labor 2 is not a beneficiary
        var welfareTypeId = (int)WelfareTypes.Marriage;

        // Act
        var result = await GetAsync<Result<CheckWelfareTypeResponseDto>>(
            $"WelfareRequests/{laborId}/{welfareTypeId}",
            AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Result should not be null.");
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.Forbidden), "API response status should be Forbidden.");
            Assert.That(result.Value, Is.Null, "Returned data should be null.");
        });
    }

    [Test]
    public async Task CheckWelfareType_ShouldReturnOk_WithEligibleFalse_WhenLimitExceeded()
    {
        // Arrange
        long laborId = 3; // Labor 3 already has a completed marriage request
        var welfareTypeId = (int)WelfareTypes.Marriage; // Marriage has MaximumLimit = 1

        // Act
        var result = await GetAsync<Result<CheckWelfareTypeResponseDto>>(
            $"WelfareRequests/{laborId}/{welfareTypeId}",
            AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Result should not be null.");
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.Ok), "API response status should be OK.");
            Assert.That(result.Value, Is.Not.Null, "Returned data should not be null.");
            Assert.That(result.Value!.IsBeneficairy, Is.False, "Should not be eligible when limit exceeded.");
        });
    }

    [Test]
    public async Task CheckWelfareType_ShouldReturnBadRequest_WhenLaborIdIsInvalid()
    {
        // Arrange
        long laborId = 0; // Invalid labor ID
        var welfareTypeId = (int)WelfareTypes.Marriage;

        // Act
        var result = await GetAsync<Result<CheckWelfareTypeResponseDto>>(
            $"WelfareRequests/{laborId}/{welfareTypeId}",
            AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Result should not be null.");
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.BadRequest),
                "API response status should be BadRequest.");
            Assert.That(result.Value, Is.Null, "Returned data should be null.");
        });
    }

    [Test]
    public async Task CheckWelfareType_ShouldReturnBadRequest_WhenWelfareTypeIdIsInvalid()
    {
        // Arrange
        var laborId = 1L;
        var welfareTypeId = 0; // Invalid welfare type ID

        // Act
        var result = await GetAsync<Result<CheckWelfareTypeResponseDto>>(
            $"WelfareRequests/{laborId}/{welfareTypeId}",
            AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Result should not be null.");
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.BadRequest),
                "API response status should be BadRequest.");
            Assert.That(result.Value, Is.Null, "Returned data should be null.");
        });
    }

    [Test]
    public async Task CheckWelfareType_ShouldReturnOk_WithEligibleTrue_ForNewbornWithinLimit()
    {
        // Arrange
        var laborId = 1L;
        var welfareTypeId = (int)WelfareTypes.NewBorn; // NewBorn has MaximumLimit = 2

        // Act
        var result = await GetAsync<Result<CheckWelfareTypeResponseDto>>(
            $"WelfareRequests/{laborId}/{welfareTypeId}",
            AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Result should not be null.");
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.Ok), "API response status should be OK.");
            Assert.That(result.Value, Is.Not.Null, "Returned data should not be null.");
            Assert.That(result.Value!.IsBeneficairy, Is.True, "Should be eligible for newborn within limit.");
        });
    }

    [Test]
    public async Task CheckWelfareType_ShouldReturnConflict_WhenInProgressRequestExists()
    {

        // Arrange - using pre-seeded data
        // Labor 1 has an in-progress NewBorn request
        const long laborId = 1L;
        const int welfareTypeId = (int)WelfareTypes.NewBorn;

        // Act
        var result = await GetAsync<Result<CheckWelfareTypeResponseDto>>(
            $"WelfareRequests/{laborId}/{welfareTypeId}",
            AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Result should not be null.");
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.Conflict), "API response status should be Conflict.");
            Assert.That(result.Value, Is.Null, "Returned data should be null.");
        });
    }
}