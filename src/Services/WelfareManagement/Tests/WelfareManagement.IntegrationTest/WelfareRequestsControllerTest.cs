using System.Reflection;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using S3.Core.Application.Models;
using S3.Core.Domain.Enums;
using S3.Core.Test.Models;
using S3.MoL.WelfareManagement.Api;
using S3.MoL.WelfareManagement.Api.WelfareRequests.Dtos;
using S3.MoL.WelfareManagement.Domain.Entities;
using S3.MoL.WelfareManagement.Domain.Enums;
using S3.MoL.WelfareManagement.Infrastructure.Services.Persistence;

namespace S3.MoL.WelfareManagement.IntegrationTest;

public class WelfareRequestsControllerTest : VirtualWebServer<Program>
{
    private WelfareManagementDbContext _welfareManagementDbContext = null!;

    protected override async void SeedTestData()
    {
        // Seed Occupations
        var occupations = new List<Occupation>
        {
            new()
            {
                OccupationId = 1,
                Code = "OCC001",
                Text = "Engineer",
                IsActive = true,
                IsDeleted = false
            }
        };
        _welfareManagementDbContext.Occupations.AddRange(occupations);

        // Seed WelfareCategories
        var welfareCategories = new List<WelfareCategory>
        {
            new()
            {
                WelfareCategoryId = 1,
                Code = "WC001",
                Text = "Social Welfare"
            }
        };
        _welfareManagementDbContext.WelfareCategories.AddRange(welfareCategories);

        // Seed WelfareTypes
        var welfareTypes = new List<WelfareType>
        {
            new()
            {
                WelfareTypeId = (int)WelfareTypes.Marriage,
                Code = "WT-M_01",
                Text = "Marriage",
                WelfareCategoryId = 1,
                MaximumLimit = 1
            },
            new()
            {
                WelfareTypeId = (int)WelfareTypes.NewBorn,
                Code = "WT-M_02",
                Text = "Newborn",
                WelfareCategoryId = 1,
                MaximumLimit = 2
            }
        };
        _welfareManagementDbContext.WelfareTypes.AddRange(welfareTypes);

        // Seed Labors
        var labors = new List<Labor>
        {
            new()
            {
                LaborId = 1,
                NationalId = "12345678901234",
                FullName = "John Doe",
                MobileNo = "1234567890123",
                OccupationId = 1,
                IsBeneficiary = true
            },
            new()
            {
                LaborId = 2,
                NationalId = "12345678901235",
                FullName = "Jane Smith",
                MobileNo = "1234567890124",
                OccupationId = 1,
                IsBeneficiary = false
            },
            new()
            {
                LaborId = 3,
                NationalId = "12345678901236",
                FullName = "Bob Johnson",
                MobileNo = "1234567890125",
                OccupationId = 1,
                IsBeneficiary = true
            }
        };
        _welfareManagementDbContext.Labors.AddRange(labors);

        // Seed RequestStatuses
        var requestStatuses = new List<RequestStatus>
        {
            new()
            {
                RequestStatusId = (int)RequestStatuses.InProgress,
                Code = "InProgress",
                Text = "InProgress"
            },
            new()
            {
                RequestStatusId = (int)RequestStatuses.Completed,
                Code = "Completed",
                Text = "Completed"
            }
        };
        _welfareManagementDbContext.RequestStatuses.AddRange(requestStatuses);

        // Seed Directorates (required for WelfareRequest)
        var directorates = new List<Directorate>
        {
            new()
            {
                DirectorateId = 1,
                Code = "DIR001",
                Text = "Test Directorate",
                IsActive = true,
                IsDeleted = false,
                GovernorateId = 1
            }
        };
        _welfareManagementDbContext.Directorates.AddRange(directorates);

        // Seed a completed welfare request for Labor 3 to test limit exceeded scenario
        var completedRequest = new SocialWelfareRequest
        {
            LaborId = 3,
            RequestStatusId = (int)RequestStatuses.Completed,
            RequestNo = "REQ001",
            DirectorateId = 1,
            EventDate = DateOnly.FromDateTime(DateTime.Now),
            NationalId = "12345678901236",
            CreatedDate = DateTime.UtcNow,
            CreatedByUserId = "test",
            CreatedByUserName = "test",
            Version = new byte[8]
        };

        // Use reflection to set the protected WelfareTypeId property
        var welfareTypeIdProperty = typeof(WelfareRequest).GetProperty(nameof(WelfareRequest.WelfareTypeId));
        welfareTypeIdProperty?.SetValue(completedRequest, (int)WelfareTypes.Marriage);

        _welfareManagementDbContext.SocialWelfareRequests.Add(completedRequest);

        await _welfareManagementDbContext.SaveChangesAsync();
    }

    protected override void OverrideServices(IServiceCollection services)
    {
        _welfareManagementDbContext = MockDataContext<WelfareManagementDbContext>(services);
        base.OverrideServices(services);
    }

    #region CheckWelfareType API Tests

    [Test]
    public async Task CheckWelfareType_ShouldReturnOk_WithEligibleTrue_WhenAllConditionsAreMet()
    {
        // Arrange
        long laborId = 1;
        int welfareTypeId = (int)WelfareTypes.Marriage;

        // Act
        var result = await GetAsync<Result<CheckWelfareTypeResponseDto>>(
            $"WelfareRequests/{laborId}/{welfareTypeId}",
            AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Result should not be null.");
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.Ok), "API response status should be OK.");
            Assert.That(result.Value, Is.Not.Null, "Returned data should not be null.");
            Assert.That(result.Value!.IsBeneficairy, Is.True, "Should be eligible for welfare.");
        });
    }

    [Test]
    public async Task CheckWelfareType_ShouldReturnNotFound_WhenLaborDoesNotExist()
    {
        // Arrange
        long laborId = 999; // Non-existent labor
        int welfareTypeId = (int)WelfareTypes.Marriage;

        // Act
        var result = await GetAsync<Result<CheckWelfareTypeResponseDto>>(
            $"WelfareRequests/{laborId}/{welfareTypeId}",
            AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Result should not be null.");
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.NotFound), "API response status should be NotFound.");
            Assert.That(result.Value, Is.Null, "Returned data should be null.");
        });
    }

    [Test]
    public async Task CheckWelfareType_ShouldReturnNotFound_WhenWelfareTypeDoesNotExist()
    {
        // Arrange
        long laborId = 1;
        int welfareTypeId = 999; // Non-existent welfare type

        // Act
        var result = await GetAsync<Result<CheckWelfareTypeResponseDto>>(
            $"WelfareRequests/{laborId}/{welfareTypeId}",
            AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Result should not be null.");
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.NotFound), "API response status should be NotFound.");
            Assert.That(result.Value, Is.Null, "Returned data should be null.");
        });
    }

    [Test]
    public async Task CheckWelfareType_ShouldReturnForbidden_WhenLaborIsNotBeneficiary()
    {
        // Arrange
        long laborId = 2; // Labor 2 is not a beneficiary
        int welfareTypeId = (int)WelfareTypes.Marriage;

        // Act
        var result = await GetAsync<Result<CheckWelfareTypeResponseDto>>(
            $"WelfareRequests/{laborId}/{welfareTypeId}",
            AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Result should not be null.");
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.Forbidden), "API response status should be Forbidden.");
            Assert.That(result.Value, Is.Null, "Returned data should be null.");
        });
    }

    [Test]
    public async Task CheckWelfareType_ShouldReturnOk_WithEligibleFalse_WhenLimitExceeded()
    {
        // Arrange
        long laborId = 3; // Labor 3 already has a completed marriage request
        int welfareTypeId = (int)WelfareTypes.Marriage; // Marriage has MaximumLimit = 1

        // Act
        var result = await GetAsync<Result<CheckWelfareTypeResponseDto>>(
            $"WelfareRequests/{laborId}/{welfareTypeId}",
            AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Result should not be null.");
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.Ok), "API response status should be OK.");
            Assert.That(result.Value, Is.Not.Null, "Returned data should not be null.");
            Assert.That(result.Value!.IsBeneficairy, Is.False, "Should not be eligible when limit exceeded.");
        });
    }

    [Test]
    public async Task CheckWelfareType_ShouldReturnBadRequest_WhenLaborIdIsInvalid()
    {
        // Arrange
        long laborId = 0; // Invalid labor ID
        int welfareTypeId = (int)WelfareTypes.Marriage;

        // Act
        var result = await GetAsync<Result<CheckWelfareTypeResponseDto>>(
            $"WelfareRequests/{laborId}/{welfareTypeId}",
            AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Result should not be null.");
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.BadRequest), "API response status should be BadRequest.");
            Assert.That(result.Value, Is.Null, "Returned data should be null.");
        });
    }

    [Test]
    public async Task CheckWelfareType_ShouldReturnBadRequest_WhenWelfareTypeIdIsInvalid()
    {
        // Arrange
        long laborId = 1;
        int welfareTypeId = 0; // Invalid welfare type ID

        // Act
        var result = await GetAsync<Result<CheckWelfareTypeResponseDto>>(
            $"WelfareRequests/{laborId}/{welfareTypeId}",
            AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Result should not be null.");
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.BadRequest), "API response status should be BadRequest.");
            Assert.That(result.Value, Is.Null, "Returned data should be null.");
        });
    }

    [Test]
    public async Task CheckWelfareType_ShouldReturnOk_WithEligibleTrue_ForNewbornWithinLimit()
    {
        // Arrange
        long laborId = 1;
        int welfareTypeId = (int)WelfareTypes.NewBorn; // NewBorn has MaximumLimit = 2

        // Act
        var result = await GetAsync<Result<CheckWelfareTypeResponseDto>>(
            $"WelfareRequests/{laborId}/{welfareTypeId}",
            AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Result should not be null.");
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.Ok), "API response status should be OK.");
            Assert.That(result.Value, Is.Not.Null, "Returned data should not be null.");
            Assert.That(result.Value!.IsBeneficairy, Is.True, "Should be eligible for newborn within limit.");
        });
    }

    [Test]
    public async Task CheckWelfareType_ShouldReturnConflict_WhenInProgressRequestExists()
    {
        // Arrange
        long laborId = 1;
        int welfareTypeId = (int)WelfareTypes.NewBorn;

        // Add an in-progress request for the same labor and welfare type
        var inProgressRequest = new SocialWelfareRequest
        {
            LaborId = 1,
            RequestStatusId = (int)RequestStatuses.InProgress,
            RequestNo = "REQ002",
            DirectorateId = 1,
            EventDate = DateOnly.FromDateTime(DateTime.Now),
            NationalId = "12345678901234",
            CreatedDate = DateTime.UtcNow,
            CreatedByUserId = "test",
            CreatedByUserName = "test",
            Version = new byte[8]
        };

        // Use reflection to set the protected WelfareTypeId property
        var welfareTypeIdProperty = typeof(WelfareRequest).GetProperty(nameof(WelfareRequest.WelfareTypeId));
        welfareTypeIdProperty?.SetValue(inProgressRequest, (int)WelfareTypes.NewBorn);

        _welfareManagementDbContext.SocialWelfareRequests.Add(inProgressRequest);
        await _welfareManagementDbContext.SaveChangesAsync();

        // Act
        var result = await GetAsync<Result<CheckWelfareTypeResponseDto>>(
            $"WelfareRequests/{laborId}/{welfareTypeId}",
            AppUtility.CreateCancelationToken());

        // Assert
        Assert.Multiple(() =>
        {
            Assert.That(result, Is.Not.Null, "Result should not be null.");
            Assert.That(result!.Status, Is.EqualTo(ResultStatus.Conflict), "API response status should be Conflict.");
            Assert.That(result.Value, Is.Null, "Returned data should be null.");
        });
    }

    #endregion
}
